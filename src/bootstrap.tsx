import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { RouterProvider } from "@tanstack/react-router";
import { initSentry } from "./instrument";
import { StrictMode } from "react";
import { createRoot } from "react-dom/client";
import { router } from "./routeTree";
import "./index.css";
import "@i18n";

export function bootstrapApp(): void {
  initSentry();

  const rootElement: HTMLElement | null = document.getElementById("root");

  if (!rootElement) {
    throw new Error('Root element with id "root" not found');
  }

  const queryClient = new QueryClient();

  createRoot(rootElement).render(
    <StrictMode>
      <QueryClientProvider client={queryClient}>
        <RouterProvider router={router} />
      </QueryClientProvider>
    </StrictMode>
  );
}
